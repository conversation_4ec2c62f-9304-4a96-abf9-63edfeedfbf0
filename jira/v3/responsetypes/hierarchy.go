package responsetypes

// Hierarchy represents the hierarchy of issue types in a project
type Hierarchy struct {
	BaseLevelID int              `json:"baseLevelId"`
	Levels      []HierarchyLevel `json:"levels"`
}

// HierarchyLevel represents a single level in the issue type hierarchy
type HierarchyLevel struct {
	AboveLevelID         int    `json:"aboveLevelId"`
	BelowLevelID         int    `json:"belowLevelId"`
	ExternalUUID         string `json:"externalUuid"`
	HierarchyLevelNumber int    `json:"hierarchyLevelNumber"`
	ID                   int    `json:"id"`
	IssueTypeIDs         []int  `json:"issueTypeIds"`
	Level                int    `json:"level"`
	Name                 string `json:"name"`
}
