package responsetypes

type ProjectLandingPageInfo struct {
	Attributes    map[string]interface{} `json:"attributes"`
	BoardID       int                    `json:"boardId"`
	BoardName     string                 `json:"boardName"`
	ProjectKey    string                 `json:"projectKey"`
	ProjectType   string                 `json:"projectType"`
	QueueCategory string                 `json:"queueCategory"`
	QueueID       int                    `json:"queueId"`
	QueueName     string                 `json:"queueName"`
	SimpleBoard   bool                   `json:"simpleBoard"`
	Simplified    bool                   `json:"simplified"`
	Url           string                 `json:"url"`
}
